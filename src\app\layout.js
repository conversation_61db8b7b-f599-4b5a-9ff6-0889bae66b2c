'use client';

import './globals.css';
import Link from 'next/link';
import { Suspense } from 'react';
import { ExperienceContextProvider } from '@/contexts/useContextExperience';
import BookingWrapper from '@/components/menu-popup/BookingWrapper';
import Footer from '@/components/Footer';

export default function RootLayout({ children }) {
  const link=['admin/dashboard','beta'];
  return (
    <html lang="en h-screen">
      <body className="font-trasandina bg-black antialiased relative flex-grow">
        <div className="site-links flex absolute w-fit h-fit flex-col-reverse items-end z-50 bottom-16 right-0 p-4 text-teal-500 underline-offset-1 capitalize gap-2">
          {link.map((i,index) =>
            <Link key={index} href={`/${i}`}>{i}</Link>
          )}
        </div>
        <ExperienceContextProvider>
          <div className='flex-grow w-full h-svh overflow-hidden'>
            {children}
          </div>
          <Suspense fallback={null}>
            <BookingWrapper/>
          </Suspense>
          <Footer/>
        </ExperienceContextProvider>
      </body>
    </html>
  );
}
