'use client'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import SpinerComponent from '../SpinerComponent'

export default function ItemInfoComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/info-markers/${id}`)
      const responseData=await serverResponse.json()
      // console.log(responseData)
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      // console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData(experienceState?.showItemInfo?.id)
  }, [experienceState?.showItemInfo?.id])

  console.log('ItemInfoComponent:',data)
  
  return (
    <div className='flex w-full h-fit text-white'>
      {/* {showError
        ? <div className='flex w-full h-full items-center justify-center'>{error}</div> 
        : <div className='flex w-full h-full items-start justify-start'>
            {loading 
              ? <div className='text-center'><span className='text-sm'>loading...</span></div>  
              : <div className='flex w-full h-full items-start justify-start'>
                  <ImageWrapperResponsive src={data?.image}/>
                </div>
            }
          </div>
      } */}
      {loading 
        ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>  
        : <div className='flex w-full h-full flex-col items-start justify-start mt-16'>
            {/* <ImageWrapperResponsive src={data?.image} className='w-full h-full'/> */}
            <div className='flex flex-col mb-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
              <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
                <div className='w-full h-fit relative'>
                  <img src={data?.image} alt='page image' className='object-cover h-auto w-full'/>
                </div>
                <div className='flex w-full h-fit gap-10 flex-col md:flex-row'>
                  <h1 className='w-full text-6xl text-left text-wrap leading-12 uppercase'>
                    {data?.title}
                  </h1>
                  <div className='flex flex-col max-w-full md:max-w-[676px] gap-10'>
                    <p className='text-left leading-7 uppercase text-3xl'>
                      {data?.body1}
                    </p>
                    <p className='text-left wfu leading-7'>
                      {data?.body2}
                    </p>
                  </div>
                </div>
                <div className='flex flex-col w-full h-fit'>
                  {data?.secondaryEntries?.map((i,index)=>(
                    <div key={index} className='flex flex-col md:flex-row md:even:flex-row-reverse w-full h-fit items-start justify-start gap-10'>
                      <div className='h-fit md:h-[422px] max-w-full md:w-[474px] relative'>
                        <img src={i?.image} alt='page image' className='object-cover w-full h-auto'/>
                      </div>
                      <div className='flex max-w-full md:w-[calc(100%-474px)] flex-col h-fit gap-5'>
                        <h1 className='w-full text-6xl text-left leading-12 uppercase'>
                          {i?.title}
                        </h1>
                        <p className='w-full text-left leading-7 text-3xl'>
                          {i?.body1}
                        </p>
                        <p className='w-full text-left leading-7'>
                          {i?.body2}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
      }
    </div>
  )
}
